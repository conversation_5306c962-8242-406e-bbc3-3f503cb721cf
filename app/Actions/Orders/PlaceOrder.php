<?php

declare(strict_types=1);

namespace App\Actions\Orders;

use App\Exceptions\EmptyCartException;
use App\Models\Cart;
use App\Models\Order;
use App\Models\SubOrder;
use App\Models\User;
use App\Modules\Cart\Services\CartService;
use App\Modules\Order\Actions\InvalidateProductPurchaseHistoryCache;
use App\Modules\Order\Events\OrderCreated;
use App\Modules\Order\Jobs\Vendor\PlaceOrderToVendor;
use App\Notifications\OrderPlacedForClinic;
use Illuminate\Support\Facades\DB;
use Spatie\SlackAlerts\Facades\SlackAlert;

final class PlaceOrder
{
    public function __construct(
        private readonly InvalidateProductPurchaseHistoryCache $invalidateProductPurchaseHistoryCache,
        private readonly CartService $cartService
    ) {}

    /**
     * Place an order.
     */
    public function handle(User $user, Cart $cart, array $data): Order
    {
        if ($cart->items()->count() === 0) {
            throw new EmptyCartException;
        }

        $order = $this->createOrder($user, $cart, $data);

        $this->placeOrdersToVendor($order);

        $this->notifyAccountAdmins($order);

        $this->notifyHighfiveSlack($order);

        $this->invalidateProductPurchaseHistoryCache->handle($order);

        OrderCreated::dispatch($order);

        return $order->fresh();
    }

    /**
     * Create an order.
     */
    private function createOrder(User $user, Cart $cart, array $data): Order
    {
        return DB::transaction(function () use ($user, $cart, $data) {
            $order = Order::createFromCart($user, $cart, $data);

            // Clean cart and invalidate cache to ensure fresh data on subsequent API calls
            $this->cartService->cleanCart($cart->clinic);

            return $order;
        });
    }

    /**
     * Place the orders to the vendor.
     */
    private function placeOrdersToVendor(Order $order): void
    {
        $order->suborders()->each(fn (SubOrder $suborder) => PlaceOrderToVendor::dispatch($suborder));
    }

    /**
     * Notify the account admins.
     */
    private function notifyAccountAdmins(Order $order): void
    {
        $order->clinic
            ->account
            ->users
            ->each
            ->notify(new OrderPlacedForClinic($order));
    }

    private function notifyHighfiveSlack(Order $order): void
    {
        if (app()->environment('production')) {

            $blocks = [
                [
                    'type' => 'header',
                    'text' => [
                        'type' => 'plain_text',
                        'text' => ':inbox_tray: Order Received',
                        'emoji' => true,
                    ],
                ],
                [
                    'type' => 'section',
                    'fields' => [
                        [
                            'type' => 'mrkdwn',
                            'text' => '*PO Number*',
                        ],
                        [
                            'type' => 'plain_text',
                            'text' => "#{$order->order_number}",
                        ],
                        [
                            'type' => 'mrkdwn',
                            'text' => '*Clinic*',
                        ],
                        [
                            'type' => 'plain_text',
                            'text' => $order->clinic->name,
                        ],
                        [
                            'type' => 'mrkdwn',
                            'text' => '*Total Vendors*',
                        ],
                        [
                            'type' => 'plain_text',
                            'text' => (string) $order->items->groupBy(fn ($item) => $item->vendor->id)->count(),
                        ],
                        [
                            'type' => 'mrkdwn',
                            'text' => '*Total Order Lines*',
                        ],
                        [
                            'type' => 'plain_text',
                            'text' => (string) $order->items()->count(),
                        ],
                    ],
                ],
                [
                    'type' => 'actions',
                    'elements' => [
                        [
                            'type' => 'button',
                            'text' => [
                                'type' => 'plain_text',
                                'text' => 'View in Nova',
                            ],
                            'url' => "https://admin.highfive.vet/nova/resources/orders/{$order->id}",
                        ],
                    ],
                ],
            ];

            SlackAlert::to('orders_log')->blocks($blocks);
        }
    }
}
