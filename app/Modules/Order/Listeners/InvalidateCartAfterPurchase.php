<?php

declare(strict_types=1);

namespace App\Modules\Order\Listeners;

use App\Modules\Cart\Services\CartService;
use App\Modules\Order\Events\OrderCreated;
use Illuminate\Contracts\Queue\ShouldQueue;

final class InvalidateCartAfterPurchase
{
    public function __construct(
        private readonly CartService $cartService
    ) {}

    public function handle(OrderCreated $event): void
    {
        // Clear all cart-related caches after successful order creation
        $this->cartService->clearAllCaches($event->order->clinic);
    }
}
