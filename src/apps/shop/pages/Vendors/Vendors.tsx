import { useEffect, useState } from 'react';
import { LoadingOverlay, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { VendorConnectModal } from './components/VendorConnectModal/VendorConnectModal';
import { VendorItem } from './components/VendorItem/VendorItem';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { VendorType } from '@/types';
import { getPriority } from './utils/getPriority';
import {
  VendorFilters,
  VendorFilterType,
} from './components/VendorFilters/VendorFilters';

export const Vendors = () => {
  const { vendors, getVendors: getVendorsRequest } = useVendorsStore();
  const { account } = useAccountStore();
  const [activeFilter, setActiveFilter] = useState<VendorFilterType>('all');

  const { apiRequest: getVendors, isLoading } = useAsyncRequest({
    apiFunc: getVendorsRequest,
  });

  useEffect(() => {
    getVendors();
  }, [getVendors]);

  const preferredVendorOrderMapState = (
    account?.gpo?.preferredVendors ?? []
  ).reduce<Record<string, number>>(
    (acc, { vendorId, order }) => ({
      ...acc,
      [vendorId]: order,
    }),
    {},
  );

  const sortedVendors = [...vendors].sort(
    (vendor1: VendorType, vendor2: VendorType) => {
      const preferredVendor1Order = preferredVendorOrderMapState[vendor1.id];
      const preferredVendor2Order = preferredVendorOrderMapState[vendor2.id];

      const vendor1IsPreferred = preferredVendor1Order !== undefined;
      const vendor1IsConnected = vendor1.status === 'connected';

      const vendor1Priority = getPriority(
        vendor1IsPreferred,
        vendor1IsConnected,
      );

      const vendor2IsPreferred = preferredVendor2Order !== undefined;
      const vendor2IsConnected = vendor2.status === 'connected';

      const vendor2Priority = getPriority(
        vendor2IsPreferred,
        vendor2IsConnected,
      );

      if (vendor1Priority !== vendor2Priority) {
        return vendor1Priority - vendor2Priority;
      }

      if (vendor1IsPreferred && vendor2IsPreferred) {
        return preferredVendor1Order - preferredVendor2Order;
      }

      return vendor1.name.localeCompare(vendor2.name);
    },
  );

  const preferredVendors = vendors.filter(
    (vendor) => preferredVendorOrderMapState[vendor.id] !== undefined,
  );
  const notConnectedVendors = vendors.filter(
    (vendor) => vendor.status === 'disconnected',
  );

  const filterCounts = {
    all: vendors.length,
    preferred: preferredVendors.length,
    disconnected: notConnectedVendors.length,
  };

  const filteredVendors = sortedVendors.filter((vendor) => {
    switch (activeFilter) {
      case 'preferred':
        return preferredVendorOrderMapState[vendor.id] !== undefined;
      case 'disconnected':
        return vendor.status === 'disconnected';
      case 'all':
      default:
        return true;
    }
  });

  return (
    <div className="mainSection">
      <div className="rounded-sm bg-white p-4">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-lg font-semibold">Connect with your vendors</h1>
          <VendorFilters
            activeFilter={activeFilter}
            onFilterChange={setActiveFilter}
            counts={filterCounts}
          />
        </div>

        {filteredVendors.length ? (
          <div className="grid grid-cols-[repeat(auto-fit,minmax(340px,1fr))] gap-4 bg-[#F9FAFB] p-3">
            {filteredVendors.map((vendor) => (
              <div key={vendor.id}>
                <VendorItem
                  vendor={vendor}
                  isPreferred={
                    preferredVendorOrderMapState[vendor.id] !== undefined
                  }
                />
              </div>
            ))}
          </div>
        ) : (
          <Flex align="center" justify="center" mih="300px">
            <Text size="lg">No vendors found</Text>
          </Flex>
        )}

        <LoadingOverlay visible={isLoading} />
        <VendorConnectModal />
      </div>
    </div>
  );
};
