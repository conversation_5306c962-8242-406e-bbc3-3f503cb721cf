import React from 'react';
import { useClinicInfo } from '../../../Settings/services/useClinicInfo';
import { VendorType } from '@/types';

export const VendorPreferenceLabel = ({ vendor }: { vendor: VendorType }) => {
  const { clinicInfo } = useClinicInfo();
  if (!clinicInfo) return null;
  const { primaryDistributor, secondaryDistributor, preferredManufacturers } =
    clinicInfo;

  const isPrimaryDistributor = primaryDistributor?.id === vendor.id;
  const isSecondaryDistributor = secondaryDistributor?.id === vendor.id;
  const isPreferredManufacturer = preferredManufacturers?.some(
    (manufacturer) => manufacturer.id === vendor.id,
  );

  return (
    <p className="text-sxs font-semibold text-[#3646AC]">
      {isPrimaryDistributor
        ? 'Primary Distributor'
        : isSecondaryDistributor
          ? 'Secondary Distributor'
          : isPreferredManufacturer
            ? 'Preferred Manufacturer'
            : null}
    </p>
  );
};
